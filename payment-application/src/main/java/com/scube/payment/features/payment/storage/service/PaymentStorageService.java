package com.scube.payment.features.payment.storage.service;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import com.scube.payment.features.payment.enums.PaymentStatus;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.processing.dto.GetPaymentResponseDto;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.SubmitPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.PaginatedResponse;
import com.scube.payment.features.payment.processing.mapper.PaymentMapper;
import com.scube.payment.features.payment.processing.mapper.RefundMapper;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.repository.PaymentStorageRepository;
import com.scube.payment.features.payment.storage.repository.RefundTransactionStorageRepository;
import com.scube.rabbit.core.AmqpGateway;
import com.scube.rabbit.core.fanout.publisher.IRabbitFanoutPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.math.BigDecimal;
import java.util.UUID;
import java.time.ZoneOffset;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentStorageService {
    private final PaymentStorageRepository paymentRepository;
    private final RefundTransactionStorageRepository refundTransactionRepository;
    private final PaymentMapper paymentMapper;
    private final AmqpGateway amqpGateway;
    private final RefundMapper refundMapper;
    private final CalculationServiceConnection calculationServiceConnection;

    /**
     * Given an amount, a user email, and maybe a transaction id, save a Payment record.
     * Returns an appropriate url redirecting to the payment provider.
     *
     * @param request
     * @return
     */
    public Payment storePayment(SubmitPaymentRequestDto request) {
        Payment payment = paymentMapper.toEntity(request);

        // Fetch order details to get the orderNumber
        try {
            OrderInvoiceResponse order = calculationServiceConnection.order().getOrder(request.getOrderId());
            if (order != null && order.getOrderNumber() != null) {
                payment.setOrderNumber(order.getOrderNumber());
            }
        } catch (Exception e) {
            log.warn("Failed to fetch order number for orderId: {}", request.getOrderId(), e);
        }

        payment.setStatus(PaymentStatus.SUCCESS); // mark em as success since no payment provider is being used
        payment.setTransactionId(UUID.randomUUID().toString());
        payment.setTransactionDate(Instant.now());
        return paymentRepository.save(payment);
    }

    /**
     * Finalize an in-process payment.
     *
     * @param paymentId
     */
    public void finalizePayment(long paymentId) {
        log.debug("Finalizing payment for id {}", paymentId);

        Payment payment = paymentRepository.findById(paymentId).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND));
        payment.setStatus(PaymentStatus.COMPLETED);
        payment.setTransactionDate(Instant.now());
        paymentRepository.save(payment);
    }

    public List<Payment> getPaymentResponseDtos() {
        log.debug("Getting all payments");
        return paymentRepository.findAll();
    }

    public void deletePayment(long id) {
        log.debug("Deleting {}", id);
        paymentRepository.deleteById(id);
    }

    public List<Payment> getPayments(UUID orderId) {
        return paymentRepository.findByOrderIdOrderByCreatedDateDesc(orderId);
    }

    public List<GetPaymentResponseDto> getPaymentResponseDtos(UUID orderId) {
        return getPayments(orderId).stream().map(GetPaymentResponseDto::new).toList();
    }

    public void storeReceiptId(UUID paymentId, UUID receiptId) {
        log.debug("Storing receiptId {} for paymentId {}", receiptId, paymentId);
        var payment = paymentRepository.findByUuidOrThrow(paymentId);
        payment.setReceiptId(receiptId);
        paymentRepository.save(payment);
    }

    public void _void(UUID paymentId) {
        var payment = paymentRepository.findByUuidOrThrow(paymentId);
        _void(payment);
    }

    public void settle(UUID paymentId) {
        var payment = paymentRepository.findByUuidOrThrow(paymentId);
        settle(payment);
    }

    public void _void(Payment payment) {
        log.debug("Voiding payment for id {}", payment.getUuid());
        payment._void();
        paymentRepository.save(payment);
        amqpGateway.publish(new OrderPaymentVoidedEvent(payment.getOrderId(), payment.getUuid()));
    }

    public PaginatedResponse<RefundTransaction> getRefundTransaction(int pageNumber, int pageSize, List<RefundStatus> statuses, LocalDate startDate, LocalDate endDate) {
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
        Page<RefundTransaction> page;

        page = refundTransactionRepository.findFilteredPaginatedRefundTransactions(startDate, endDate, statuses, pageable);

        PaginatedResponse<RefundTransaction> response = new PaginatedResponse<>();
        response.setContent(page.getContent());
        response.setPageNumber(page.getNumber() + 1);
        response.setPageSize(page.getSize());
        response.setTotalElements(page.getTotalElements());
        response.setTotalPages(page.getTotalPages());

        return response;
    }

    public List<RefundTransaction> getRefundTransactionsByOrderId(UUID orderId) {
        return refundTransactionRepository.findByOrderId(orderId);
    }

    public RefundTransaction storeRefund(RefundPaymentRequestDto refundRequest) {
        RefundTransaction refundTransaction = refundMapper.fromRequestToEntity(refundRequest);
        return refundTransactionRepository.save(refundTransaction);
    }

    public void updateRefund(RefundTransaction refundTransaction, String refundId) {
        refundTransaction.setRefundReference(refundId);
        refundTransaction.setIsOnlineTransaction(true);
        refundTransaction.setStatus(RefundStatus.PROCESSING);
        refundTransaction.setTransactionDate(Instant.now());
        refundTransactionRepository.save(refundTransaction);
    }

    public void updateOfflineRefund(RefundTransaction refundTransaction) {
        refundTransaction.setIsOnlineTransaction(false);
        refundTransaction.setStatus(RefundStatus.COMPLETED);
        refundTransaction.setRefundedTs(Instant.now());
        refundTransactionRepository.save(refundTransaction);
    }

    public void updateRefundStatus(RefundTransaction refundTransaction, RefundStatus refundStatus) {
        refundTransaction.setStatus(refundStatus);
        refundTransaction.setRefundedTs(Instant.now());
        refundTransactionRepository.save(refundTransaction);
    }

    public void settle(Payment payment) {
        log.debug("Settling payment for id {}", payment.getUuid());
        payment.settle();
        paymentRepository.save(payment);
    }

    public Payment getByPaymentReference(String paymentReference) {
        return paymentRepository.findByPaymentReference(paymentReference).orElseThrow();
    }

    public RefundTransaction getByRefundReference(String refundReference) {
        return refundTransactionRepository.findByRefundReference(refundReference)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "RefundTransaction not found for reference: " + refundReference));
    }

    public BigDecimal getRefundedAmount(UUID orderId) {
        return refundTransactionRepository.findByOrderId(orderId).stream()
                .filter(rt -> rt.getStatus() == RefundStatus.COMPLETED)
                .map(RefundTransaction::getRefundedTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public boolean existsByPaymentReference(String paymentReference) {
        return paymentRepository.existsByPaymentReference(paymentReference);
    }

    public List<GetPaymentResponseDto> filterPayments(String name) {
        log.debug("Filtering payments by name: {}", name);
        List<Payment> payments = paymentRepository.findByOrderFilter(name);
        return payments.stream().map(GetPaymentResponseDto::new).toList();
    }

    public record OrderPaymentRefundedEvent(UUID orderId, UUID paymentId) implements IRabbitFanoutPublisher {}
    public record OrderPaymentVoidedEvent(UUID orderId, UUID paymentId) implements IRabbitFanoutPublisher {}
}