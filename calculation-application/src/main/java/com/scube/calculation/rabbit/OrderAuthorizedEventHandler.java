package com.scube.calculation.rabbit;

import com.scube.calculation.repository.OrderRepository;
import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.UUID;

/**
 * When payment token is generated and order is authorized, update finance status to AUTHORIZED
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OrderAuthorizedEventHandler extends FanoutListener<OrderAuthorizedEventHandler.OrderAuthorizedEvent> {
    private final OrderRepository orderRepository;

    @Transactional
    @Override
    public void consume(OrderAuthorizedEvent event) {
        log.debug("Received OrderAuthorizedEvent for orderId: {}", event.orderId());
        
        var order = orderRepository.findById(UUID.fromString(event.orderId()))
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "No such order with id " + event.orderId()));

        order.markOrderAuthorized();
        orderRepository.save(order);
        
        log.debug("Order {} finance status updated to AUTHORIZED", event.orderId());
    }

    public record OrderAuthorizedEvent(String orderId) implements IRabbitFanoutSubscriber {
    }
}



