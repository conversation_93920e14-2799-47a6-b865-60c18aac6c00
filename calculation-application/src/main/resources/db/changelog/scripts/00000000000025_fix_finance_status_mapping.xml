<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Update ORDER_COMPLETED to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderCompletedToActive_v2" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status IN ('ORDER_COMPLETED', 'COMPLETED');
        </sql>
    </changeSet>

    <!-- Update ORDER_COMPLETED to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderCompletedToActive_v2" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status IN ('ORDER_COMPLETED', 'COMPLETED');
        </sql>
    </changeSet>

    <!-- Update finance_status to CAPTURED for paid orders in order_ table -->
    <changeSet id="updateFinanceStatusToCaptured_v2" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'CAPTURED'
            WHERE status IN ('ORDER_ACTIVE', 'ORDER_PAID', 'PAID')
            AND (finance_status IS NULL OR finance_status IN ('UNPAID', 'PAID'));
        </sql>
    </changeSet>

    <!-- Update finance_status to CAPTURED for paid orders in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToCaptured_v2" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'CAPTURED'
            WHERE status IN ('ORDER_ACTIVE', 'ORDER_PAID', 'PAID')
            AND (finance_status IS NULL OR finance_status IN ('UNPAID', 'PAID'));
        </sql>
    </changeSet>

    <!-- Update finance_status to REFUNDED for ORDER_REFUNDED status in order_ table -->
    <changeSet id="updateFinanceStatusToRefunded_v2" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'REFUNDED'
            WHERE status = 'ORDER_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to REFUNDED for ORDER_REFUNDED status in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToRefunded_v2" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'REFUNDED'
            WHERE status = 'ORDER_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to PARTIALLY_REFUNDED for ORDER_PARTIALLY_REFUNDED status in order_ table -->
    <changeSet id="updateFinanceStatusToPartiallyRefunded_v2" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'PARTIALLY_REFUNDED'
            WHERE status = 'ORDER_PARTIALLY_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'PARTIALLY_REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to PARTIALLY_REFUNDED for ORDER_PARTIALLY_REFUNDED status in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToPartiallyRefunded_v2" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'PARTIALLY_REFUNDED'
            WHERE status = 'ORDER_PARTIALLY_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'PARTIALLY_REFUNDED');
        </sql>
    </changeSet>

</databaseChangeLog>

