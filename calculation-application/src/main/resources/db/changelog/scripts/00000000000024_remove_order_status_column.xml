<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Drop order_status column from order_ table -->
    <changeSet id="dropColumn_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="order_" columnName="order_status"/>
        </preConditions>
        <dropColumn tableName="order_" columnName="order_status"/>
    </changeSet>

    <!-- Drop order_status column from audit_log_order_ table -->
    <changeSet id="dropColumn_audit_log_order_status" author="system">
        <preConditions onFail="MARK_RAN">
            <columnExists tableName="audit_log_order_" columnName="order_status"/>
        </preConditions>
        <dropColumn tableName="audit_log_order_" columnName="order_status"/>
    </changeSet>

</databaseChangeLog>

