<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Update ORDER_PAID to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderStatusToActive" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_PAID';
        </sql>
    </changeSet>

    <!-- Update ORDER_PAID to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderStatusToActive" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_PAID';
        </sql>
    </changeSet>

    <!-- Update ORDER_LOCKED to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderLockedToActive" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_LOCKED';
        </sql>
    </changeSet>

    <!-- Update ORDER_LOCKED to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderLockedToActive" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status = 'ORDER_LOCKED';
        </sql>
    </changeSet>

    <!-- Update ORDER_COMPLETED to ORDER_ACTIVE in order_ table -->
    <changeSet id="updateOrderCompletedToActive" author="system">
        <sql>
            UPDATE order_
            SET status = 'ORDER_ACTIVE'
            WHERE status IN ('ORDER_COMPLETED', 'COMPLETED');
        </sql>
    </changeSet>

    <!-- Update ORDER_COMPLETED to ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogOrderCompletedToActive" author="system">
        <sql>
            UPDATE audit_log_order_
            SET status = 'ORDER_ACTIVE'
            WHERE status IN ('ORDER_COMPLETED', 'COMPLETED');
        </sql>
    </changeSet>

    <!-- Update finance_status to CAPTURED if status is PAID/ORDER_PAID/ORDER_ACTIVE in order_ table -->
    <changeSet id="updateFinanceStatusToCaptured" author="system">
        <validCheckSum>9:e972ed0eccf0f4b8f22e159d20e8046a</validCheckSum>
        <validCheckSum>9:4bd0e44d08640c32511f82e5dae03855</validCheckSum>
        <validCheckSum>9:1a708e8117bb14b4de509648cb0d97fb</validCheckSum>
        <sql>
            UPDATE order_
            SET finance_status = 'CAPTURED'
            WHERE status IN ('ORDER_ACTIVE', 'ORDER_PAID', 'PAID')
            AND (finance_status IS NULL OR finance_status IN ('UNPAID', 'PAID'));
        </sql>
    </changeSet>

    <!-- Update finance_status to CAPTURED if status is PAID/ORDER_PAID/ORDER_ACTIVE in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToCaptured" author="system">
        <validCheckSum>9:1190ed5bfb7acd4fab5294d2d5cb5a7e</validCheckSum>
        <validCheckSum>9:bcf325f5e3096c31d49d0331e18e8142</validCheckSum>
        <validCheckSum>9:7fb110d1ab2b3e3fe4ca0802d0a6f64d</validCheckSum>
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'CAPTURED'
            WHERE status IN ('ORDER_ACTIVE', 'ORDER_PAID', 'PAID')
            AND (finance_status IS NULL OR finance_status IN ('UNPAID', 'PAID'));
        </sql>
    </changeSet>

    <!-- Update finance_status to REFUNDED for ORDER_REFUNDED status in order_ table -->
    <changeSet id="updateFinanceStatusToRefunded" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'REFUNDED'
            WHERE status = 'ORDER_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to REFUNDED for ORDER_REFUNDED status in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToRefunded" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'REFUNDED'
            WHERE status = 'ORDER_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to PARTIALLY_REFUNDED for ORDER_PARTIALLY_REFUNDED status in order_ table -->
    <changeSet id="updateFinanceStatusToPartiallyRefunded" author="system">
        <sql>
            UPDATE order_
            SET finance_status = 'PARTIALLY_REFUNDED'
            WHERE status = 'ORDER_PARTIALLY_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'PARTIALLY_REFUNDED');
        </sql>
    </changeSet>

    <!-- Update finance_status to PARTIALLY_REFUNDED for ORDER_PARTIALLY_REFUNDED status in audit_log_order_ table -->
    <changeSet id="updateAuditLogFinanceStatusToPartiallyRefunded" author="system">
        <sql>
            UPDATE audit_log_order_
            SET finance_status = 'PARTIALLY_REFUNDED'
            WHERE status = 'ORDER_PARTIALLY_REFUNDED'
            AND (finance_status IS NULL OR finance_status != 'PARTIALLY_REFUNDED');
        </sql>
    </changeSet>

</databaseChangeLog>

